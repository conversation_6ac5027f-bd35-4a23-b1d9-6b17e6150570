<template>
  <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
    <h2 class="text-xl font-semibold mb-4">Thêm <PERSON><PERSON><PERSON></h2>

    <!-- Trạng thái <PERSON> -->
    <div v-if="isLoading" class="flex items-center justify-center py-10">
      <svg class="w-8 h-8 mr-3 -ml-1 text-blue-500 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="text-gray-600"><PERSON><PERSON> tải dữ liệu...</span>
    </div>

    <!-- Trạng thái Lỗi -->
    <div v-else-if="error" class="px-4 py-3 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
      <strong class="font-bold">Lỗi!</strong>
      <span class="block sm:inline"> {{ error }}</span>
    </div>

    <!-- Form nhập liệu -->
    <div v-else class="flex flex-col lg:flex-row gap-4">
      <!-- Cột thông tin chi tiết (trái) -->
      <div class="w-full lg:w-3/5 space-y-2">
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
          <template v-for="field in displayFields" :key="field.key">
            <div class="space-y-1">
              <label :for="field.key" class="block text-sm font-medium text-gray-700">{{ field.label }}</label>
              <VueDatePicker
                v-if="field.key === 'nam_xd' || field.key === 'nam_sd'"
                v-model.number="formData[field.key]"
                year-picker
                :name="field.key"
                :id="field.key"
                placeholder="Chọn năm"
                :year-range="[1900, new Date().getFullYear() + 10]"
                format="yyyy"
                auto-apply
                :clearable="true"
                class="w-full"
              />
              <select
                v-else-if="field.key === 'phuongthuc'"
                :name="field.key"
                :id="field.key"
                v-model="formData[field.key]"
                class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="" disabled>Chọn phương thức</option>
                <option v-for="option in phuongThucOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </option>
              </select>
              <select
                v-else-if="field.key === 'loai_ct'"
                :name="field.key"
                :id="field.key"
                v-model="formData[field.key]"
                class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="" disabled>Chọn loại công trình</option>
                <option value="Nhỏ">Nhỏ</option>
                <option value="Vừa">Vừa</option>
                <option value="Lớn">Lớn</option>
              </select>
              <template v-else-if="field.key === 'id_qt'">
                <select
                  :name="field.key"
                  :id="field.key"
                  v-model="formData[field.key]"
                  :disabled="isLoadingQuyetToan"
                  class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="" disabled>--- Chọn quyết toán ---</option>
                  <option v-for="option in quyetToanOptions" :key="option.id" :value="option.id">
                    {{ option.qd_quyettoan }}
                  </option>
                </select>
                <div v-if="quyetToanError" class="mt-1 text-sm text-red-600">
                  {{ quyetToanError }}
                </div>
              </template>
              <input
                v-else-if="field.key === 'nguyengia'"
                type="number"
                :name="field.key"
                :id="field.key"
                v-model.number="formData[field.key]"
                class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-100 cursor-not-allowed"
                disabled
                placeholder="Giá trị sẽ tự động cập nhật"
              />
              <input
                v-else-if="field.key === 'dt_dat'"
                type="number"
                :name="field.key"
                :id="field.key"
                :step="field.key === 'dt_dat' ? 'any' : '1'"
                v-model.number="formData[field.key]"
                class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
               <textarea
                v-else-if="field.key === 'chuthich'"
                :name="field.key"
                :id="field.key"
                v-model="formData[field.key]"
                rows="2"
                class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              ></textarea>
              <input
                v-else
                type="text"
                :name="field.key"
                :id="field.key"
                v-model="formData[field.key]"
                :readonly="field.key === 'id_xa' || field.key === 'tenxa'"
                :class="[
                  'block w-full p-2 border border-gray-300 rounded-md shadow-sm sm:text-sm',
                  field.key === 'id_xa' || field.key === 'tenxa'
                    ? 'bg-gray-100 text-gray-600 cursor-not-allowed'
                    : 'focus:ring-blue-500 focus:border-blue-500'
                ]"
              />
            </div>
          </template>
        </div>
      </div>
      <!-- Cột bản đồ (phải) -->
      <div class="w-full lg:w-2/5">
        <div ref="mapContainer" class="h-80 lg:h-full rounded-md bg-gray-200">
          <!-- Leaflet map will be initialized here -->
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="flex items-center justify-end px-6 py-2 space-x-3 rounded-b-lg mt-6">
      <button
        @click="resetForm"
        type="button"
        :disabled="isSubmitting"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Đặt lại
      </button>
      <button
        @click="submitForm"
        type="button"
        :disabled="isSubmitting"
        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span v-if="isSubmitting" class="flex items-center"><svg class="w-4 h-4 mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Đang thêm...</span>
        <span v-else>Thêm mới</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed, nextTick } from 'vue';
import axios from 'axios';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-draw/dist/leaflet.draw.css';

// Import VueDatePicker
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';

import 'leaflet-draw';

// Define Emits để thông báo cho parent component
const emit = defineEmits(['refresh-data'])

// State
const isLoading = ref(false);
const error = ref<string | null>(null);
const isSubmitting = ref(false);
const mapContainer = ref<HTMLElement | null>(null);
const mapInstance = ref<L.Map | null>(null);
const editableLayers = ref<L.FeatureGroup | null>(null);

// Form data
const formData = ref<Record<string, any>>({
  ten: '',
  quymo_ct: '',
  loai_ct: '',
  nam_xd: '',
  nam_sd: '',
  dt_dat: '',
  tinhtrang: '',
  quytrinh_vh: '',
  quytrinh_bt: '',
  dv_quanly: '',
  phuongthuc: '',
  chuthich: '',
  id_xa: '',
  tenxa: '',
  id_qt: '',
  nguyengia: null
})

// Định nghĩa các tùy chọn cho trường "Phương thức quản lý"
const phuongThucOptions = ref([
  { value: 'Sử dụng chung', label: 'Sử dụng chung' },
  { value: 'Trực tiếp tổ chức thực hiện khai thác', label: 'Trực tiếp tổ chức thực hiện khai thác' },
]);

const drawControl = ref<L.Control.Draw | null>(null);

// Quyết toán options
interface QuyetToanOption {
  id: string;
  nguyengia: number | null;
  [key: string]: any;
}
const quyetToanOptions = ref<QuyetToanOption[]>([]);
const isLoadingQuyetToan = ref(false);
const quyetToanError = ref<string | null>(null);

// Mapping for Vietnamese field names
const vietnameseFieldNames: Record<string, string> = {
  id_qt: "QĐ Quyết Toán",
  nguyengia: "Nguyên Giá",
  ten: "Tên Công Trình",
  quymo_ct: "Quy Mô Công Trình",
  loai_ct: "Loại Công Trình",
  nam_xd: "Năm Xây Dựng",
  nam_sd: "Năm Sử Dụng",
  dt_dat: "Diện Tích Đất (m²)",
  tinhtrang: "Tình Trạng",
  quytrinh_vh: "Quy Trình Vận Hành",
  quytrinh_bt: "Quy Trình Bảo Trì",
  dv_quanly: "Đơn Vị Quản Lý",
  phuongthuc: "Phương Thức Quản Lý",
  chuthich: "Chú Thích",
  tenxa: "Tên Xã"
};

// Define the desired order of fields for display
const fieldOrder: string[] = [
  "ten",
  "loai_ct",
  "quymo_ct",
  "tinhtrang",
  "nam_xd",
  "nam_sd",
  "tenxa",
  "id_qt",
  "nguyengia",
  "dt_dat",
  "dv_quanly",
  "phuongthuc",
  "quytrinh_vh",
  "quytrinh_bt",
  "chuthich"
];

const displayFields = computed(() => {
  const orderedFields: { key: string; value: any; label: string }[] = [];
  const availableKeys = Object.keys(formData.value);

  // Add fields based on fieldOrder
  fieldOrder.forEach(key => {
    if (availableKeys.includes(key)) {
      orderedFields.push({ key, value: formData.value[key], label: formatLabel(key) });
    }
  });

  return orderedFields;
});

// Helper function to format snake_case keys to Title Case labels
const formatLabel = (key: string): string => {
  if (!key) return '';
  if (vietnameseFieldNames[key]) {
    return vietnameseFieldNames[key];
  }
  const words = key.split('_');
  return words
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Reset form
const resetForm = () => {
  Object.keys(formData.value).forEach(key => {
    if (key === 'nguyengia') {
      formData.value[key] = null;
    } else {
      formData.value[key] = '';
    }
  });
  // Reset map if exists
  if (mapInstance.value && editableLayers.value) {
    editableLayers.value.clearLayers();
  }
};

// Function to find xa by coordinates using spatial query
const findXaByCoordinates = async (longitude: number, latitude: number) => {
  try {
    const response = await axios.post('/api/spatial/find-xa-by-coordinates', {
      longitude,
      latitude
    });

    if (response.data.success && response.data.data) {
      // Update id_xa and ten_xa in formData
      formData.value.id_xa = response.data.data.id_xa;
      formData.value.tenxa = response.data.data.ten_xa;
      console.log('Thông tin xã đã được cập nhật:', {
        id_xa: response.data.data.id_xa,
        tenxa: response.data.data.ten_xa
      });
    } else {
      // Clear xa information if not found
      formData.value.id_xa = null;
      formData.value.tenxa = null;
      console.log('Không tìm thấy xã tại tọa độ này');
    }
  } catch (error: any) {
    console.error('Lỗi khi tìm kiếm thông tin xã:', error);
    // Don't clear existing xa information on error to avoid data loss
  }
};

// Helper to update asset coordinates
const updateAssetCoordinates = async (latlng: L.LatLng) => {
  // Update geometry in formData
  if (!formData.value.geometry) {
    formData.value.geometry = { type: 'Point', coordinates: [0,0] };
  }
  formData.value.geometry.type = 'Point';
  formData.value.geometry.coordinates = [latlng.lng, latlng.lat];

  // Automatically find xa information when coordinates change
  await findXaByCoordinates(latlng.lng, latlng.lat);
};

const initMap = async () => {
  if (mapContainer.value && !mapInstance.value) {
    const lat = 10.7769; // Default to HCMC lat
    const lng = 106.7009; // Default to HCMC lng
    const zoom = 12;

    mapInstance.value = L.map(mapContainer.value).setView([lat, lng], zoom);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(mapInstance.value);

    // Initialize FeatureGroup for draw controls
    editableLayers.value = new L.FeatureGroup();
    mapInstance.value.addLayer(editableLayers.value);

    // Initialize Draw Control
    const drawOptions = {
      position: 'topright',
      draw: {
        polygon: false,
        polyline: false,
        rectangle: false,
        circle: false,
        circlemarker: false,
        marker: { draggable: true },
      },
      edit: {
        featureGroup: editableLayers.value! as any,
        remove: true,
      },
    };

    drawControl.value = new L.Control.Draw(drawOptions as any);
    mapInstance.value.addControl(drawControl.value as any);

    // Event for when a new shape is created
    mapInstance.value.on(L.Draw.Event.CREATED, async (event) => {
      const layer = (event as L.DrawEvents.Created).layer;
      if (layer instanceof L.Marker) {
        // Add drag event listener to new marker
        layer.on('dragend', async (dragEvent) => {
          const draggedMarker = dragEvent.target as L.Marker;
          await updateAssetCoordinates(draggedMarker.getLatLng());
        });

        editableLayers.value?.clearLayers(); // Clear previous markers, assuming only one point
        editableLayers.value?.addLayer(layer);
        await updateAssetCoordinates(layer.getLatLng());
      }
    });

    // Event for when shapes are edited
    mapInstance.value.on(L.Draw.Event.EDITED, (event) => {
      const layers = (event as L.DrawEvents.Edited).layers;
      layers.eachLayer((layer) => {
        if (layer instanceof L.Marker) {
          updateAssetCoordinates(layer.getLatLng());
        }
      });
    });

     // Event for when shapes are deleted
     mapInstance.value.on(L.Draw.Event.DELETED, () => {
        if (formData.value.geometry) {
            formData.value.geometry = null;
        }
    });
  }
};

const destroyMap = () => {
  if (mapInstance.value) {
    mapInstance.value.remove();
    mapInstance.value = null;
  }
};

const loadQuyetToanOptions = async () => {
  try {
    isLoadingQuyetToan.value = true;
    quyetToanError.value = null;
    const response = await fetch('/api/quyet-toan-options');
    if (!response.ok) {
      throw new Error('Không thể tải danh sách quyết toán');
    }
    const data = await response.json();
    quyetToanOptions.value = data.data || [];
  } catch (error: any) {
    console.error('Error loading quyet toan options:', error);
    quyetToanError.value = error.message || 'Có lỗi xảy ra khi tải danh sách quyết toán.';
  } finally {
    isLoadingQuyetToan.value = false;
  }
};

// Submit form
const submitForm = async () => {
  try {
    isSubmitting.value = true;
    error.value = null;

    // Validate required fields
    if (!formData.value.ten) {
      error.value = 'Vui lòng nhập tên công trình';
      return;
    }

    // Prepare data for API
    const apiData: any = { ...formData.value };

    // Add geometry if coordinates are available from map
    if (formData.value.geometry && formData.value.geometry.coordinates) {
      apiData.geometry = formData.value.geometry;
    }

    // Remove internal fields that shouldn't be sent to API
    delete apiData.geometry; // Will be re-added above if needed

    const response = await axios.post('/api/taisan/congdap', apiData);

    if (response.data.success) {
      // Success feedback
      error.value = null;
      resetForm();
      // Trigger refresh data
      emit('refresh-data');
      // Show success message
      alert('Thêm mới thành công!');
    } else {
      throw new Error(response.data.message || 'Thêm mới thất bại');
    }
  } catch (err: any) {
    console.error('Lỗi khi thêm mới:', err);
    error.value = err.response?.data?.message || err.message || 'Có lỗi xảy ra khi thêm mới. Vui lòng thử lại.';
  } finally {
    isSubmitting.value = false;
  }
};

// Watch for changes in id_qt to update nguyengia
watch(() => formData.value.id_qt, (newIdQt) => {
  if (newIdQt && quyetToanOptions.value.length > 0) {
    const selectedQuyetToan = quyetToanOptions.value.find(option => option.id === newIdQt);
    if (selectedQuyetToan && typeof selectedQuyetToan.nguyengia !== 'undefined') {
      formData.value.nguyengia = selectedQuyetToan.nguyengia;
    } else {
      formData.value.nguyengia = null;
    }
  } else {
    formData.value.nguyengia = null;
  }
});

onMounted(async () => {
  await loadQuyetToanOptions();
  await nextTick();
  await initMap();
});

onUnmounted(() => {
  destroyMap();
});
</script>
